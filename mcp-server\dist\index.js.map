{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAEA,mEAAmE;AACnE,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,8CAA8C;AAC9C,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,gBAAgB;AAChB,MAAM,MAAM,GAAG;IACb,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;IACjE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,0BAA0B;IAC9D,UAAU,EAAE;QACV,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE,OAAO;KACjB;CACF,CAAC;AAEF,eAAe;AACf,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;IAC7B,OAAO,EAAE,MAAM,CAAC,YAAY;IAC5B,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,WAAW,EAAE,MAAM,CAAC,OAAO;KAC5B;IACD,OAAO,EAAE,KAAK;CACf,CAAC,CAAC;AAEH,yCAAyC;AACzC,+GAA+G;AAE/G,oBAAoB;AACpB,MAAM,KAAK,GAAG;IACZ,oBAAoB;IACpB;QACE,IAAI,EAAE,mBAAmB;QACzB,WAAW,EAAE,0DAA0D;QACvE,WAAW,EAAE;YACX,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+BAA+B,EAAE;aACxE;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB;KACF;IACD;QACE,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,qCAAqC;QAClD,WAAW,EAAE;YACX,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+BAA+B,EAAE;aACxE;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB;KACF;IACD,aAAa;IACb;QACE,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,uBAAuB;QACpC,WAAW,EAAE;YACX,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+BAA+B,EAAE;gBACvE,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE;gBAC3F,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,4BAA4B,EAAE;gBAC1E,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE;gBACvD,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gCAAgC,EAAE;gBAC9E,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0CAA0C,EAAE;aACvF;YACD,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC;SACzC;KACF;IACD;QACE,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,wDAAwD;QACrE,WAAW,EAAE;YACX,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+BAA+B,EAAE;gBACvE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8BAA8B,EAAE;gBACrE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yCAAyC,EAAE;gBACjF,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oDAAoD,EAAE;gBAC7F,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0CAA0C,EAAE;gBACvF,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,uCAAuC,EAAE;gBAClF,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,4DAA4D,EAAE;gBACzG,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,6BAA6B,EAAE;aAC5E;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB;KACF;CACF,CAAC;AAEF,yBAAyB;AACzB,MAAM,QAAQ,GAAG;IACf,uBAAuB;IACvB,KAAK,CAAC,iBAAiB,CAAC,IAAS;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAC7E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAS;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;QACrE,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,gBAAgB;IAChB,KAAK,CAAC,WAAW,CAAC,IAAS;QACzB,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAS;QACxB,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAEnG,gCAAgC;QAChC,MAAM,MAAM,GAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QACpC,IAAI,MAAM;YAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QACnC,IAAI,UAAU;YAAE,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;QAC/C,IAAI,QAAQ;YAAE,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzC,IAAI,SAAS,KAAK,SAAS;YAAE,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAC1D,IAAI,WAAW;YAAE,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;QAElD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,mBAAmB,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;CACF,CAAC;AAEF,sCAAsC;AACtC,MAAM,cAAc;IAClB;QACE,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IACpD,CAAC;IAEO,aAAa;QACnB,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,6DAA6D;IAC/D,CAAC;IAED,KAAK,CAAC,GAAG;QACP,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;QAEtB,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAElE,6DAA6D;QAC7D,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAExB,0DAA0D;QAC1D,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;YACnC,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,eAAe,CAAC;YAC5E,cAAc,EAAE,CAAC,cAAc,CAAC;SACjC,CAAC,CAAC,CAAC;QAEJ,gDAAgD;QAChD,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,4BAA4B,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,CAAC;gBACH,0BAA0B;gBAC1B,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;gBAEzB,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBAClD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,OAAO,EAAE,KAAK;wBACd,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,IAAI;wBACtB,KAAK,EAAE;4BACL,IAAI,EAAE,CAAC,KAAK;4BACZ,OAAO,EAAE,iBAAiB;yBAC3B;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAEvD,IAAI,QAAQ,CAAC;gBAEb,2BAA2B;gBAC3B,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;oBACvB,KAAK,YAAY;wBACf,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;wBAC9C,QAAQ,GAAG;4BACT,OAAO,EAAE,KAAK;4BACd,EAAE,EAAE,OAAO,CAAC,EAAE;4BACd,MAAM,EAAE;gCACN,eAAe,EAAE,YAAY;gCAC7B,YAAY,EAAE;oCACZ,KAAK,EAAE,EAAE;iCACV;gCACD,UAAU,EAAE;oCACV,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;oCAC5B,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO;iCACnC;6BACF;yBACF,CAAC;wBACF,MAAM;oBAER,KAAK,YAAY;wBACf,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;wBAC9C,QAAQ,GAAG;4BACT,OAAO,EAAE,KAAK;4BACd,EAAE,EAAE,OAAO,CAAC,EAAE;4BACd,MAAM,EAAE;gCACN,KAAK,EAAE,KAAK;6BACb;yBACF,CAAC;wBACF,MAAM;oBAER,KAAK,YAAY;wBACf,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;wBAC9C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;wBAEjD,IAAI,CAAC,QAAQ,CAAC,IAA6B,CAAC,EAAE,CAAC;4BAC7C,QAAQ,GAAG;gCACT,OAAO,EAAE,KAAK;gCACd,EAAE,EAAE,OAAO,CAAC,EAAE;gCACd,KAAK,EAAE;oCACL,IAAI,EAAE,CAAC,KAAK;oCACZ,OAAO,EAAE,iBAAiB,IAAI,EAAE;iCACjC;6BACF,CAAC;wBACJ,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC;gCACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAA6B,CAAC,CAAC,IAAI,CAAC,CAAC;gCACnE,QAAQ,GAAG;oCACT,OAAO,EAAE,KAAK;oCACd,EAAE,EAAE,OAAO,CAAC,EAAE;oCACd,MAAM,EAAE;wCACN,OAAO,EAAE;4CACP;gDACE,IAAI,EAAE,MAAM;gDACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;6CACtC;yCACF;qCACF;iCACF,CAAC;4BACJ,CAAC;4BAAC,OAAO,KAAK,EAAE,CAAC;gCACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gCAC/C,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gCAC9E,QAAQ,GAAG;oCACT,OAAO,EAAE,KAAK;oCACd,EAAE,EAAE,OAAO,CAAC,EAAE;oCACd,KAAK,EAAE;wCACL,IAAI,EAAE,CAAC,KAAK;wCACZ,OAAO,EAAE,YAAY;qCACtB;iCACF,CAAC;4BACJ,CAAC;wBACH,CAAC;wBACD,MAAM;oBAER;wBACE,QAAQ,GAAG;4BACT,OAAO,EAAE,KAAK;4BACd,EAAE,EAAE,OAAO,CAAC,EAAE;4BACd,KAAK,EAAE;gCACL,IAAI,EAAE,CAAC,KAAK;gCACZ,OAAO,EAAE,qBAAqB,OAAO,CAAC,MAAM,EAAE;6BAC/C;yBACF,CAAC;gBACN,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;gBAC7C,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErB,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI;oBACxB,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC,KAAK;wBACZ,OAAO,EAAE,gBAAgB;qBAC1B;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;YAC/B,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAIH,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE;YAC/B,OAAO,CAAC,GAAG,CAAC,gDAAgD,IAAI,EAAE,CAAC,CAAC;YACpE,OAAO,CAAC,GAAG,CAAC,mCAAmC,IAAI,MAAM,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,mCAAmC,IAAI,SAAS,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,oBAAoB;AACpB,KAAK,UAAU,IAAI;IACjB,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;QACpC,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,6BAA6B;AAC7B,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;IAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACvE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACrB,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;IAC/C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}