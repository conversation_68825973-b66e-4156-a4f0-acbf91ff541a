#!/usr/bin/env node

import { EventSource } from 'eventsource';
import axios from 'axios';

const MCP_URL = 'http://localhost:3012/mcp';
const HEALTH_URL = 'http://localhost:3012/health';

async function testMCPConnection() {
  console.log('🧪 Testing MCP Server Connection...\n');

  // 1. Test health endpoint
  try {
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await axios.get(HEALTH_URL);
    console.log('✅ Health check:', healthResponse.data);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return;
  }

  // 2. Test SSE connection
  console.log('\n2️⃣ Testing SSE connection...');
  
  const eventSource = new EventSource(MCP_URL);
  let sessionId = null;

  eventSource.onopen = () => {
    console.log('✅ SSE connection opened');
  };

  eventSource.onmessage = (event) => {
    console.log('📨 SSE message:', event);
  };

  eventSource.addEventListener('endpoint', (event) => {
    console.log('🔗 Endpoint event received:', event.data);
    const match = event.data.match(/sessionId=([^&]+)/);
    if (match) {
      sessionId = match[1];
      console.log('🆔 Session ID:', sessionId);
      
      // Now test MCP protocol
      testMCPProtocol(sessionId);
    }
  });

  eventSource.onerror = (error) => {
    console.error('❌ SSE error:', error);
  };

  // Keep connection alive for testing
  setTimeout(() => {
    console.log('\n⏰ Closing connection after 30 seconds...');
    eventSource.close();
  }, 30000);
}

async function testMCPProtocol(sessionId) {
  console.log('\n3️⃣ Testing MCP Protocol...');
  
  try {
    // Test MCP initialization
    console.log('📤 Sending MCP initialization...');
    const initResponse = await axios.post(`${MCP_URL}?sessionId=${sessionId}`, {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'test-client',
          version: '1.0.0'
        }
      }
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Initialization response:', JSON.stringify(initResponse.data, null, 2));

    // Test tools list
    console.log('\n📤 Requesting tools list...');
    const toolsResponse = await axios.post(`${MCP_URL}?sessionId=${sessionId}`, {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/list',
      params: {}
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Tools response:', JSON.stringify(toolsResponse.data, null, 2));

  } catch (error) {
    console.error('❌ MCP Protocol error:', error.response?.data || error.message);
  }
}

// Run the test
testMCPConnection().catch(console.error);
