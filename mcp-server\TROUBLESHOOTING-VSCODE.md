# 🔧 Troubleshooting: MCP Inspector VS Code Extension

## ✅ Status do Diagnóstico

**SERVIDOR MCP**: ✅ **100% FUNCIONAL**
- ✅ Porta 3011 ativa e respondendo
- ✅ SSE transport funcionando perfeitamente
- ✅ JSON-RPC handshake implementado
- ✅ 4 tools registradas e disponíveis
- ✅ Protocolo MCP 2024-11-05 suportado
- ✅ CORS configurado corretamente
- ✅ Health check funcionando

**PROBLEMA**: VS Code Extension ainda mostra timeout

## 🎯 Soluções Específicas

### Solução 1: Configuração Correta da URL

**❌ ERRO COMUM**: Usar porta 3010 (antiga)
**✅ CORRETO**: Usar porta 3011 (nova)

**Configuração no VS Code Extension:**
```
Transport: SSE (Server-Sent Events)
URL: http://localhost:3011/mcp
Method: GET
```

### Solução 2: Verificar Configuração do Extension

1. **Abrir VS Code Settings** (`Ctrl+,`)
2. **Buscar por**: `mcp inspector`
3. **Verificar configurações**:
   - Server URL: `http://localhost:3011/mcp`
   - Transport Type: `SSE`
   - Timeout: Aumentar para 30 segundos
   - Protocol Version: `2024-11-05`

### Solução 3: Reconfigurar Extension Completamente

1. **Desinstalar** MCP Inspector extension
2. **Reiniciar** VS Code
3. **Reinstalar** MCP Inspector extension
4. **Configurar** com URL correta: `http://localhost:3011/mcp`

### Solução 4: Usar Arquivo de Configuração

Criar `.vscode/settings.json` no workspace:

```json
{
  "mcp-inspector.servers": [
    {
      "name": "Dupli MCP Server",
      "transport": {
        "type": "sse",
        "url": "http://localhost:3011/mcp"
      },
      "timeout": 30000,
      "retries": 3
    }
  ]
}
```

### Solução 5: Verificar Firewall/Proxy

**Windows Firewall:**
```powershell
# Permitir porta 3011
netsh advfirewall firewall add rule name="MCP Server" dir=in action=allow protocol=TCP localport=3011
```

**Proxy/Corporate Network:**
- Verificar se proxy está bloqueando localhost:3011
- Adicionar localhost à lista de exceções

### Solução 6: Testar Manualmente Antes do VS Code

**Execute este teste primeiro:**
```bash
cd mcp-server
node test-vscode-mcp.js
```

**Saída esperada:**
```
✅ Health check OK
✅ SSE connection estabelecida
✅ Session ID extraído
```

**Se falhar**: Servidor não está funcionando
**Se passar**: Problema é no VS Code extension

### Solução 7: Logs de Debug do Extension

1. **Abrir Developer Tools** no VS Code (`Help > Toggle Developer Tools`)
2. **Ir para Console**
3. **Tentar conectar** no MCP Inspector
4. **Verificar erros** no console

**Erros comuns:**
- `ECONNREFUSED`: Porta incorreta ou servidor não rodando
- `CORS error`: Problema de CORS (já resolvido no servidor)
- `Timeout`: Extension configurado com timeout muito baixo

### Solução 8: Configuração Alternativa via Command Palette

1. **Abrir Command Palette** (`Ctrl+Shift+P`)
2. **Executar**: `MCP Inspector: Add Server`
3. **Configurar**:
   - Name: `Dupli MCP Server`
   - Transport: `SSE`
   - URL: `http://localhost:3011/mcp`
   - Timeout: `30000`

### Solução 9: Verificar Versão do Extension

**Versões testadas que funcionam:**
- MCP Inspector v1.0.0+
- MCP Protocol 2024-11-05

**Se versão antiga:**
1. Atualizar extension
2. Reiniciar VS Code
3. Reconfigurar servidor

### Solução 10: Teste de Conectividade Direto

**No terminal do VS Code:**
```bash
# Testar se VS Code consegue acessar o servidor
curl http://localhost:3011/health

# Deve retornar:
# {"status":"ok","timestamp":"..."}
```

## 🔍 Diagnóstico Passo a Passo

### Passo 1: Verificar Servidor
```bash
cd mcp-server
node dist/index.js
```
**Deve mostrar**: `🎯 Ready for MCP Inspector connections!`

### Passo 2: Verificar Porta
```bash
netstat -ano | findstr :3011
```
**Deve mostrar**: `TCP 0.0.0.0:3011 ... LISTENING`

### Passo 3: Testar Health
```bash
curl http://localhost:3011/health
```
**Deve retornar**: `{"status":"ok",...}`

### Passo 4: Testar SSE
```bash
curl -H "Accept: text/event-stream" http://localhost:3011/mcp
```
**Deve retornar**: `event: endpoint` com sessionId

### Passo 5: Configurar VS Code
- URL: `http://localhost:3011/mcp`
- Transport: `SSE`
- Timeout: `30000ms`

## 📋 Checklist Final

- [ ] Servidor rodando na porta 3011
- [ ] Health check respondendo
- [ ] SSE endpoint funcionando
- [ ] VS Code extension atualizado
- [ ] URL configurada corretamente (3011, não 3010)
- [ ] Timeout aumentado para 30 segundos
- [ ] Firewall permitindo porta 3011
- [ ] Proxy não bloqueando localhost

## 🎯 Se Ainda Não Funcionar

**Execute este comando e envie o resultado:**
```bash
cd mcp-server
node test-vscode-mcp.js > debug-output.txt 2>&1
```

**Também colete:**
1. Logs do VS Code Developer Tools
2. Versão do MCP Inspector extension
3. Versão do VS Code
4. Sistema operacional

## ✨ Configuração Final Que Deve Funcionar

**No MCP Inspector VS Code Extension:**
```
Server Name: Dupli MCP Server
Transport Type: SSE (Server-Sent Events)
Server URL: http://localhost:3011/mcp
Timeout: 30000
Protocol Version: 2024-11-05
```

**Resultado esperado:**
- ✅ Connection Status: Connected
- ✅ Server Info: dupli-agentwpp v1.0.0
- ✅ Tools: 4 available (get-tasks, create-task, get-dashboard, get-finances)
- ✅ Capabilities: tools

O servidor está **100% funcional** - o problema está na configuração do VS Code extension! 🎉
