#!/usr/bin/env node

import axios from 'axios';

const MCP_URL = 'http://localhost:3012/mcp';

async function testSimplePost() {
  console.log('🧪 Testing simple POST to MCP endpoint...\n');

  try {
    // Test direct POST without session
    console.log('📤 Sending direct POST request...');
    const response = await axios.post(MCP_URL, {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'test-client',
          version: '1.0.0'
        }
      }
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });
    
    console.log('✅ Response received:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    console.error('Status:', error.response?.status);
    console.error('Headers:', error.response?.headers);
  }
}

testSimplePost().catch(console.error);
